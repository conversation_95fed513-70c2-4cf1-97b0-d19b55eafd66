package com.bigtimes.dq.tool.service.Impl;

import static com.bigtimes.dq.common.utils.JsonUtil.extractValuesFromJson;

import com.bigtimes.dq.common.dto.GetDatasourceResultDTO;
import com.bigtimes.dq.common.dto.instance.MinAndMaxDTO;
import com.bigtimes.dq.common.enums.AuditTypeEnum;
import com.bigtimes.dq.common.enums.InstanceStateEnum;
import com.bigtimes.dq.common.enums.RuleLevelEnum;
import com.bigtimes.dq.common.enums.TimeVariableEnum;
import com.bigtimes.dq.common.enums.VariableEnum;
import com.bigtimes.dq.common.enums.VerificationEnum;
import com.bigtimes.dq.common.utils.CacheUtil;
import com.bigtimes.dq.common.utils.ConvertUtils;
import com.bigtimes.dq.common.utils.EncryptUtil;
import com.bigtimes.dq.common.utils.JsonUtil;
import com.bigtimes.dq.common.utils.LocalCacheUtil;
import com.bigtimes.dq.common.vo.rule.RuleVO;
import com.bigtimes.dq.common.wrapper.ApiResponse;
import com.bigtimes.dq.dao.cache.DatasourceCache;
import com.bigtimes.dq.dao.entity.Category;
import com.bigtimes.dq.dao.entity.ConfigFile;
import com.bigtimes.dq.dao.entity.Datasource;
import com.bigtimes.dq.dao.entity.DatasourceFile;
import com.bigtimes.dq.dao.entity.RuleDefinition;
import com.bigtimes.dq.dao.entity.RuleInstance;
import com.bigtimes.dq.dao.entity.SystemVariable;
import com.bigtimes.dq.dao.mapper.DatasourceFileMapper;
import com.bigtimes.dq.dao.mapper.DatasourceMapper;
import com.bigtimes.dq.dao.mapper.TaskInstanceMapper;
import com.bigtimes.dq.dao.param.DbDatasourceParam;
import com.bigtimes.dq.dao.param.DbToDbDatasourceParam;
import com.bigtimes.dq.dao.param.ErrorTypeParam;
import com.bigtimes.dq.dao.param.HqlDatasourceParam;
import com.bigtimes.dq.dao.param.KerberosParam;
import com.bigtimes.dq.dao.param.OtherParams;
import com.bigtimes.dq.dao.param.RuleDsOtherParams;
import com.bigtimes.dq.dao.param.SqlDatasourceParam;
import com.bigtimes.dq.dao.param.SystemVariableParam;
import com.bigtimes.dq.server.config.SqlProperties;
import com.bigtimes.dq.server.connection.ConnectionPool;
import com.bigtimes.dq.server.connection.Impl.HqlConnectionPool;
import com.bigtimes.dq.server.connection.Impl.JdbcConnectionPool;
import com.bigtimes.dq.server.util.BaseUtil;
import com.bigtimes.dq.server.util.DatasourceConnectionParamUtil;
import com.bigtimes.dq.server.util.HqlDataapiUtil;
import com.bigtimes.dq.server.util.OSUtils;
import com.bigtimes.dq.server.util.SqlUtil;
import com.bigtimes.dq.tool.config.ServiceConfig;
import com.bigtimes.dq.tool.service.RuleService;
import com.bigtimes.dq.tool.utils.DateUtil;
import com.bigtimes.dq.tool.utils.DiffCsvWriter;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.PrivilegedExceptionAction;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dolphinscheduler.plugin.datasource.api.datasource.DataSourceProcessor;
import org.apache.dolphinscheduler.plugin.datasource.api.utils.DataSourceUtil;
import org.apache.dolphinscheduler.plugin.datasource.param.ImpalaConnectionParam;
import org.apache.dolphinscheduler.plugin.datasource.param.ImpalaDataSourceProcessor;
import org.apache.dolphinscheduler.spi.datasource.ConnectionParam;
import org.apache.dolphinscheduler.spi.enums.DbType;
import org.apache.dolphinscheduler.spi.utils.PropertyUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-02-28
 */
@Slf4j
@Service
public class RuleServiceImpl extends BaseService implements RuleService {
    
    @Autowired
    private ServiceConfig serviceConfig;
    
    @Autowired
    private DatasourceMapper datasourceMapper;
    
    @Autowired
    private SqlProperties sqlProperties;
    
    @Autowired
    private DatasourceFileMapper datasourceFileMapper;
    
    @Autowired
    private DatasourceConnectionParamUtil datasourceConnectionParamUtil;
    
    @Value("${kerberos.path}")
    private String publicKeysStorePath;
    
    @Value("${error-writer.file-path}")
    private String errorWriterFilePath;
	@Autowired
	private TaskInstanceMapper taskInstanceMapper;
    
    @Override
    public ApiResponse getCategoryTree() {
        List<Category> list = getCategory(-1);
        return ApiResponse.ofSuccess(list);
    }
    
    @Override
    public ApiResponse getRuleById(Integer id) {
        List<RuleVO> list = ruleDefinitionMapper.selectRuleByCategoryId(id);
        return ApiResponse.ofSuccess(list);
    }
    
    @Override
    public Integer runTaskByCategory(List<Integer> ids, String env) throws Exception {
        //查找所有目录下的所有规则
        List<RuleDefinition> ruleDefinitionList = getRuleDefinitionList(ids);
        boolean status = true;
        for (RuleDefinition ruleDefinition : ruleDefinitionList) {
            //根据规则创建实例
            RuleInstance ruleInstance = createInstance(ruleDefinition, env);
            //执行实例
            if (ruleDefinition.getEnable() == true) {
                status = runRuleInstance(ruleInstance, env);
                if (RuleLevelEnum.HIGH.equals(ruleDefinition.getLevel()) && !status) {
                    status = false;
                }
                //执行稽核
                if (status) {
                    status = runAudit(ruleInstance, env);
                }
            } else {
                log.info("规则未启用，跳过执行,ruleId:{}", ruleDefinition.getId());
                return 0;
            }
        }
        if (status) {
            return 0;
        }
        return 1;
    }
    
    @Override
    public Integer runTaskByRuleId(List<Integer> ids, List<Integer> taskInstanceIds, String env)
        throws Exception {
        boolean status = true;
        for (int i = 0; i < ids.size(); i++) {
            Integer id = ids.get(i);
            Integer taskInstanceId = taskInstanceIds.get(i);
            RuleDefinition ruleDefinition = ruleDefinitionMapper.selectRuleDefinitionById(id);
            //根据规则创建实例
            RuleInstance ruleInstance = createInstance(ruleDefinition, taskInstanceId, env);
            //执行实例
            if (ruleDefinition.getEnable() == true) {
                status = runRuleInstance(ruleInstance, env);
                
                if (RuleLevelEnum.HIGH.equals(ruleDefinition.getLevel()) && !status) {
                    status = false;
                }
                //执行稽核
                Integer auditStatus = 1;
                if (status) {
                    status = runAudit(ruleInstance, env);
                }
            } else {
                log.info("规则未启用，跳过执行,ruleId:{}", id);
                return 0;
            }
        }
        
        if (status) {
            return 0;
        }
        return 1;
    }
    
    /**
     * 执行稽核
     *
     * @param ruleInstance
     */
    private Boolean runAudit(RuleInstance ruleInstance, String env) {
        RuleDefinition ruleDefinition = ruleDefinitionMapper.selectRuleDefinitionById(
            ruleInstance.getRuleDefinitionId());
        AuditTypeEnum type = ruleDefinition.getType();
        if (type == AuditTypeEnum.DBTODB) {
            try {
                // 获取源端和目标端连接
                DbToDbDatasourceParam dbToDbDatasourceParam = JsonUtil.readValue(
                    ruleDefinition.getDatasourceParams(), DbToDbDatasourceParam.class);
                Datasource sourceDatasource = datasourceMapper.selectDatasourceById(
                    dbToDbDatasourceParam.getSourceDs());
                Datasource targetDatasource = datasourceMapper.selectDatasourceById(
                    dbToDbDatasourceParam.getTargetDs());
                // 解析连接参数
                SqlDatasourceParam sourceParam = datasourceConnectionParamUtil.parseConnectionParams(
                    sourceDatasource.getConnectionParams(), sourceDatasource.getDsType());
                SqlDatasourceParam targetParam = datasourceConnectionParamUtil.parseConnectionParams(
                    targetDatasource.getConnectionParams(), targetDatasource.getDsType());
                String sourceUrl = sourceParam.getSqlJdbcUrl();
                String targetUrl = targetParam.getSqlJdbcUrl();
                String sourceUser = sourceParam.getSqlUsername();
                String sourcePwd = EncryptUtil.aesDecrypt(sourceParam.getSqlPassword());
                String targetUser = targetParam.getSqlUsername();
                String targetPwd = EncryptUtil.aesDecrypt(targetParam.getSqlPassword());
                String sourceSql = ruleInstance.getSourceSql();
                String targetSql = ruleInstance.getTargetSql();
                // dbType 40 teradata, 27 doris, 0 mysql, other other
                String sourceType = sourceDatasource.getDsType() == 40 ? "teradata"
                    : (sourceDatasource.getDsType() == 27 ? "doris"
                        : (sourceDatasource.getDsType() == 0 ? "mysql" : "other"));
                String targetType = targetDatasource.getDsType() == 40 ? "teradata"
                    : (targetDatasource.getDsType() == 27 ? "doris"
                        : (targetDatasource.getDsType() == 0 ? "mysql" : "other"));
                //流式对比仅支持 Doris/Teradata 之间的对比
                boolean streamSupported =
                    ("mysql".equals(sourceType) || "doris".equals(sourceType) || "teradata".equals(
                        sourceType))
                        && ("mysql".equals(targetType) || "doris".equals(targetType)
                        || "teradata".equals(targetType));
//                "mysql".equals(targetType) ||
//                "mysql".equals(sourceType) ||
                if (streamSupported) {
                    log.info("[DQ流式对比] 源端类型:{} 目标端类型:{}，使用流式对比。", sourceType,
                        targetType);
                    try (
                        Connection conn1 = DriverManager.getConnection(
                            appendStreamParams(sourceUrl), sourceUser, sourcePwd);
                        Connection conn2 = DriverManager.getConnection(
                            appendStreamParams(targetUrl), targetUser, targetPwd)
                    ) {
                        // 获取任务名称和规则名称用于文件命名
                        String taskName = getTaskNameFromInstance(ruleInstance);
                        String ruleName = ruleDefinition.getName();

                        StreamDiffResult diffResult = streamDiffAndPreview(conn1, sourceSql, sourceType,
                            conn2, targetSql, targetType, taskName, ruleName);

                        if (diffResult.hasDiff) {
                            // 数据不一致
                            String enhancedErrorValue;
                            if (diffResult.totalProcessedRows <= 1) {
                                // 单行数据差异，不提及文件
                                enhancedErrorValue = String.format("%s (单行数据差异)", diffResult.errorValue);
                            } else {
                                // 多行数据差异
                                enhancedErrorValue = String.format("%s (共发现 %d 行差异，总处理 %d 行)",
                                    diffResult.errorValue, diffResult.totalDiffCount, diffResult.totalProcessedRows);
                                if (diffResult.diffFilePath != null) {
                                    enhancedErrorValue += String.format("，差异详情已记录到文件: %s", diffResult.diffFilePath);
                                }
                            }

                            ruleInstance.setErrorValue(enhancedErrorValue);
                            ruleInstance.setSourceResult(diffResult.diffSourceRowJson);
                            ruleInstance.setTargetResult(diffResult.diffTargetRowJson);
                            ruleInstance.setState(InstanceStateEnum.AUDITING_FAILURE);
                        } else {
                            // 数据一致
                            ruleInstance.setErrorValue(String.format("数据一致，共处理 %d 行", diffResult.totalProcessedRows));
                            ruleInstance.setSourceResult(diffResult.previewSourceRowsJson);
                            ruleInstance.setTargetResult(diffResult.previewTargetRowsJson);
                            ruleInstance.setState(InstanceStateEnum.AUDITING_SUCCESS);
                        }

                        if ("prod".equals(env)) {
                            ruleInstanceMapper.updateById(ruleInstance);
                        }
                        log.info("environment is {}, run audit ruleInstance is : {}", env,
                            ruleInstance);
                        return !diffResult.hasDiff;
                    }
                } else {
                    log.info("[DQ全量对比] 源端类型:{} 目标端类型:{}，使用全量List<Map>对比。",
                        sourceType, targetType);
                    // 全量对比逻辑：先获取数据，再进行比对
                    String sourceResult = getResult(sourceSql, sourceDatasource, "");
                    String targetResult = getResult(targetSql, targetDatasource, "");
                    ruleInstance.setSourceResult(sourceResult);
                    ruleInstance.setTargetResult(targetResult);
                    
                    List<ErrorTypeParam> typeList = JsonUtil.readValueList(
                        ruleDefinition.getErrorType(), ErrorTypeParam.class);
                    List<Map<String, String>> sourceResultList = JsonUtil.readMapList(sourceResult);
                    List<Map<String, String>> targetResultList = JsonUtil.readMapList(targetResult);
                    boolean state = true;
                    StringBuilder allErrorCols = new StringBuilder();
                    int diffCount = 0;
                    int diffLimit = 10;
                    
                    if (sourceResultList != null && targetResultList != null) {
                        String lastType = null;
                        for (int i = 0; i < sourceResultList.size(); i++) {
                            try {
                                String jsonString = JsonUtil.toJsonString(
                                    typeList.get(i % typeList.size()));
                                ErrorTypeParam judgeParam = JsonUtil.readValue(jsonString,
                                    ErrorTypeParam.class);
                                String currentType =
                                    judgeParam.getErrorType() != null ? judgeParam.getErrorType()
                                        .name() : "UNKNOWN";
                                if (!Objects.equals(currentType, lastType)) {
                                    log.info("[DQ全量对比] 当前对比类型: {}", currentType);
                                    lastType = currentType;
                                }
                                Map<String, String> sourceRow = sourceResultList.get(i);
                                Map<String, String> targetRow = targetResultList.get(i);
                                List<String> errorCols = new ArrayList<>();
                                // 逐列对比
                                for (String col : sourceRow.keySet()) {
                                    String sourceVal = sourceRow.get(col);
                                    String targetVal = targetRow.get(col);
                                    boolean colState = true;
                                    switch (judgeParam.getErrorType()) {
                                        case PRECISE:
                                            colState = Objects.equals(sourceVal, targetVal);
                                            break;
                                        case ALLOWABLE_ERROR:
                                            BigDecimal source = new BigDecimal(sourceVal);
                                            BigDecimal target = new BigDecimal(targetVal);
                                            if (BigDecimal.ZERO.equals(source)) {
                                                colState =
                                                    target.compareTo(judgeParam.getErrorValue())
                                                        != 1;
                                                break;
                                            }
                                            if (BigDecimal.ZERO.equals(target)) {
                                                colState =
                                                    source.compareTo(judgeParam.getErrorValue())
                                                        != 1;
                                                break;
                                            }
                                            BigDecimal divide = source.subtract(target);
                                            BigDecimal subtract1 = divide.abs();
                                            colState =
                                                subtract1.compareTo(judgeParam.getErrorValue())
                                                    != 1;
                                            break;
                                        case IGNORE_DECIMAL_PLACE:
                                            BigDecimal sourceValue = new BigDecimal(sourceVal);
                                            BigDecimal targetValue = new BigDecimal(targetVal);
                                            sourceValue = sourceValue.setScale(
                                                judgeParam.getErrorValue().intValue(),
                                                BigDecimal.ROUND_DOWN);
                                            targetValue = targetValue.setScale(
                                                judgeParam.getErrorValue().intValue(),
                                                BigDecimal.ROUND_DOWN);
                                            BigDecimal subtract = targetValue.subtract(sourceValue);
                                            colState = subtract.compareTo(BigDecimal.ZERO) == 0;
                                            break;
                                        case EXPECTED_VALUE_COMPARISON:
                                            OtherParams expectedValue = JsonUtil.readValue(
                                                targetVal, OtherParams.class);
                                            BigDecimal min = expectedValue.getMin();
                                            BigDecimal max = expectedValue.getMax();
                                            BigDecimal dbSourceValue = new BigDecimal(sourceVal);
                                            colState = dbSourceValue.compareTo(min) >= 0
                                                && dbSourceValue.compareTo(max) <= 0;
                                            break;
                                        default:
                                            colState = false;
                                            break;
                                    }
                                    if (!colState) {
                                        errorCols.add(col);
                                    }
                                }
                                if (!errorCols.isEmpty()) {
                                    state = false;
                                    diffCount++;
                                    allErrorCols.append("行: ").append(i + 1).append(", 字段: ")
                                        .append(String.join(",", errorCols)).append("; ");
                                    if (diffCount >= diffLimit) {
                                        allErrorCols.append("...diff limit reached; ");
                                        break;
                                    }
                                }
                            } catch (IndexOutOfBoundsException exception) {
                                state = false;
                                log.error(
                                    exception.toString() + "\n结果数量大于偏差类型数量，请检查规则");
                                ruleInstance.setErrorValue("结果数量大于偏差类型数量，请检查规则");
                                break;
                            } catch (Exception e) {
                                state = false;
                                log.error("程序执行错误：{}", e);
                                ruleInstance.setErrorValue(e.toString());
                                break;
                            }
                            if (diffCount >= diffLimit) {
                                break;
                            }
                        }
                        if (!state && allErrorCols.length() > 0) {
                            ruleInstance.setErrorValue(allErrorCols.toString());
                        }
                    } else {
                        state = false;
                        ruleInstance.setErrorValue("源端或目标端结果为空");
                    }
                    ruleInstance.setState(state ? InstanceStateEnum.AUDITING_SUCCESS
                        : InstanceStateEnum.AUDITING_FAILURE);
                    if ("prod".equals(env)) {
                        ruleInstanceMapper.updateById(ruleInstance);
                    }
                    log.info("environment is {}, run audit ruleInstance is : {}", env,
                        ruleInstance);
                    return state;
                }
            } catch (Exception e) {
                ruleInstance.setErrorValue("流式对比异常:" + e.getMessage());
                ruleInstance.setState(InstanceStateEnum.AUDITING_FAILURE);
                if ("prod".equals(env)) {
                    ruleInstanceMapper.updateById(ruleInstance);
                }
                log.error("流式对比异常", e);
                return false;
            }
        }
        List<ErrorTypeParam> typeList = JsonUtil.readValueList(ruleDefinition.getErrorType(),
            ErrorTypeParam.class);
        List<Map<String, String>> sourceResultList = JsonUtil.readMapList(
            ruleInstance.getSourceResult());
        List<Map<String, String>> targetResultList = JsonUtil.readMapList(
            ruleInstance.getTargetResult());
        boolean state = true;
        StringBuilder allErrorCols = new StringBuilder();
        int diffCount = 0;
        int diffLimit = 10;
        if (sourceResultList != null && targetResultList != null) {
            String lastType = null;
            for (int i = 0; i < sourceResultList.size(); i++) {
                try {
                    String jsonString = JsonUtil.toJsonString(typeList.get(i % typeList.size()));
                    ErrorTypeParam judgeParam = JsonUtil.readValue(jsonString,
                        ErrorTypeParam.class);
                    String currentType =
                        judgeParam.getErrorType() != null ? judgeParam.getErrorType().name()
                            : "UNKNOWN";
                    if (!Objects.equals(currentType, lastType)) {
                        log.info("[DQ全量对比] 当前对比类型: {}", currentType);
                        lastType = currentType;
                    }
                    Map<String, String> sourceRow = sourceResultList.get(i);
                    Map<String, String> targetRow = targetResultList.get(i);
                    List<String> errorCols = new ArrayList<>();
                    // 逐列对比
                    for (String col : sourceRow.keySet()) {
                        String sourceVal = sourceRow.get(col);
                        String targetVal = targetRow.get(col);
                        boolean colState = true;
                        switch (judgeParam.getErrorType()) {
                            case PRECISE:
                                colState = Objects.equals(sourceVal, targetVal);
                                break;
                            case ALLOWABLE_ERROR:
                                BigDecimal source = new BigDecimal(sourceVal);
                                BigDecimal target = new BigDecimal(targetVal);
                                if (BigDecimal.ZERO.equals(source)) {
                                    colState = target.compareTo(judgeParam.getErrorValue()) != 1;
                                    break;
                                }
                                if (BigDecimal.ZERO.equals(target)) {
                                    colState = source.compareTo(judgeParam.getErrorValue()) != 1;
                                    break;
                                }
                                BigDecimal divide = source.subtract(target);
                                BigDecimal subtract1 = divide.abs();
                                colState = subtract1.compareTo(judgeParam.getErrorValue()) != 1;
                                break;
                            case IGNORE_DECIMAL_PLACE:
                                BigDecimal sourceValue = new BigDecimal(sourceVal);
                                BigDecimal targetValue = new BigDecimal(targetVal);
                                sourceValue = sourceValue.setScale(
                                    judgeParam.getErrorValue().intValue(), BigDecimal.ROUND_DOWN);
                                targetValue = targetValue.setScale(
                                    judgeParam.getErrorValue().intValue(), BigDecimal.ROUND_DOWN);
                                BigDecimal subtract = targetValue.subtract(sourceValue);
                                colState = subtract.compareTo(BigDecimal.ZERO) == 0;
                                break;
                            case EXPECTED_VALUE_COMPARISON:
                                OtherParams expectedValue = JsonUtil.readValue(targetVal,
                                    OtherParams.class);
                                BigDecimal min = expectedValue.getMin();
                                BigDecimal max = expectedValue.getMax();
                                BigDecimal dbSourceValue = new BigDecimal(sourceVal);
                                colState = dbSourceValue.compareTo(min) >= 0
                                    && dbSourceValue.compareTo(max) <= 0;
                                break;
                            default:
                                colState = false;
                                break;
                        }
                        if (!colState) {
                            errorCols.add(col);
                        }
                    }
                    if (!errorCols.isEmpty()) {
                        state = false;
                        diffCount++;
                        allErrorCols.append("行: ").append(i + 1).append(", 字段: ")
                            .append(String.join(",", errorCols)).append("; ");
                        if (diffCount >= diffLimit) {
                            allErrorCols.append("...diff limit reached; ");
                            break;
                        }
                    }
                } catch (IndexOutOfBoundsException exception) {
                    state = false;
                    log.error(exception.toString() + "\n结果数量大于偏差类型数量，请检查规则");
                    ruleInstance.setErrorValue("结果数量大于偏差类型数量，请检查规则");
                    break;
                } catch (Exception e) {
                    state = false;
                    log.error("程序执行错误：{}", e);
                    ruleInstance.setErrorValue(e.toString());
                    break;
                }
                if (diffCount >= diffLimit) {
                    break;
                }
            }
            if (!state && allErrorCols.length() > 0) {
                ruleInstance.setErrorValue(allErrorCols.toString());
            }
        } else {
            state = false;
            ruleInstance.setErrorValue("源端或目标端结果为空");
        }
        ruleInstance.setState(InstanceStateEnum.AUDITING_FAILURE);
        if (state) {
            ruleInstance.setState(InstanceStateEnum.AUDITING_SUCCESS);
        }
        if ("prod".equals(env)) {
            ruleInstanceMapper.updateById(ruleInstance);
        }
        log.info("environment is {}, run audit ruleInstance is : {}", env, ruleInstance);
        return state;
    }
    
    /**
     * 创建实例
     *
     * @param ruleDefinition
     * @return
     */
    private RuleInstance createInstance(RuleDefinition ruleDefinition, String env)
        throws Exception {
        Date date = DateUtil.getCurrentDate();
        log.info("data {}", date.getTime());
        RuleInstance ruleInstance = new RuleInstance();
        StringBuilder name = new StringBuilder(ruleDefinition.getName());
        name.append("-").append("0").append("-").append(System.currentTimeMillis());
        ruleInstance.setRuleDefinitionId(ruleDefinition.getId());
        ruleInstance.setName(name.toString());
        if (ruleDefinition.getEnable() == true) {
            ruleInstance.setState(InstanceStateEnum.RUNNING);
        } else {
            //已下线规则
            ruleInstance.setState(InstanceStateEnum.INVALID);
        }
        ruleInstance.setScheduleTime(date);
        
        AuditTypeEnum type = ruleDefinition.getType();
        
        String sourceSql = "";
        switch (type) {
            case DBTODB:
                sourceSql = getSourceSql(ruleDefinition, date);
                String targetSql = getTargetSql(ruleDefinition, date);
                ruleInstance.setTargetSql(targetSql);
                break;
            
            case DB:
                sourceSql = getSql(ruleDefinition, date);
                String targetResult = null;
//                String targetResult = ruleDefinition.getOtherParams();
//                OtherParams otherParams = JsonUtil.readValue(targetResult, OtherParams.class);
                List<ErrorTypeParam> errorTypeParamList = JsonUtil.readValueList(
                    ruleDefinition.getErrorType(), ErrorTypeParam.class);
                ArrayNode otherParamsList = JsonUtil.getArrayNode();
                for (int i = 0; i < errorTypeParamList.size(); i++) {
                    String jsonString = JsonUtil.toJsonString(
                        errorTypeParamList.get(i)); //转化为Json格式字符串
                    ErrorTypeParam errorTypeParam = JsonUtil.readValue(jsonString,
                        ErrorTypeParam.class);
                    OtherParams otherParams = errorTypeParam.getOtherParams();
                    MinAndMaxDTO minAndMaxDTO = new MinAndMaxDTO();
                    minAndMaxDTO.setMax(otherParams.getMax());
                    minAndMaxDTO.setMin(otherParams.getMin());
                    otherParamsList.add(JsonUtil.readTree(JsonUtil.toJsonString(minAndMaxDTO)));
                }
//                MinAndMaxDTO minAndMaxDTO = new MinAndMaxDTO();
//                minAndMaxDTO.setMax(otherParams.getMax());
//                minAndMaxDTO.setMin(otherParams.getMin());
//                ruleInstance.setTargetResult(JsonUtil.toJsonString(minAndMaxDTO));
                ruleInstance.setTargetResult(JsonUtil.toJsonString(otherParamsList));
                ruleInstance.setTargetSql("");
                break;
        }
        ruleInstance.setSourceSql(sourceSql);
        ruleInstance.setBatchno(0);
        if ("prod".equals(env)) {
            ruleInstanceMapper.insert(ruleInstance);
        }
        log.info("env is: {}, rule instance is: {}", env, ruleInstance);
        return ruleInstance;
    }
    
    /**
     * 创建实例
     *
     * @param ruleDefinition
     * @return
     * @Param taskInstanceId
     */
    private RuleInstance createInstance(RuleDefinition ruleDefinition, Integer taskInstanceId,
        String env
    ) throws Exception {
        Date date = DateUtil.getCurrentDate();
        log.info("date {}", date.getTime());
        RuleInstance ruleInstance = new RuleInstance();
        StringBuilder name = new StringBuilder(ruleDefinition.getName());
        name.append("-").append("0").append("-").append(System.currentTimeMillis());
        ruleInstance.setRuleDefinitionId(ruleDefinition.getId());
        ruleInstance.setName(name.toString());
        if (ruleDefinition.getEnable() == true) {
            ruleInstance.setState(InstanceStateEnum.RUNNING);
        } else {
            //已下线规则
            ruleInstance.setState(InstanceStateEnum.INVALID);
        }
        ruleInstance.setScheduleTime(date);
        ruleInstance.setTaskInstanceId(taskInstanceId);
        
        AuditTypeEnum type = ruleDefinition.getType();
        
        String sourceSql = "";
        switch (type) {
            case DBTODB:
                sourceSql = getSourceSql(ruleDefinition, date);
                String targetSql = getTargetSql(ruleDefinition, date);
                ruleInstance.setTargetSql(targetSql);
                break;
            
            case DB:
                sourceSql = getSql(ruleDefinition, date);
                String targetResult = null;
//                String targetResult = ruleDefinition.getOtherParams();
//                OtherParams otherParams = JsonUtil.readValue(targetResult, OtherParams.class);
                List<ErrorTypeParam> errorTypeParamList = JsonUtil.readValueList(
                    ruleDefinition.getErrorType(), ErrorTypeParam.class);
                ArrayNode otherParamsList = JsonUtil.getArrayNode();
                for (int i = 0; i < errorTypeParamList.size(); i++) {
                    String jsonString = JsonUtil.toJsonString(
                        errorTypeParamList.get(i)); //转化为Json格式字符串
                    ErrorTypeParam errorTypeParam = JsonUtil.readValue(jsonString,
                        ErrorTypeParam.class);
                    OtherParams otherParams = errorTypeParam.getOtherParams();
                    MinAndMaxDTO minAndMaxDTO = new MinAndMaxDTO();
                    minAndMaxDTO.setMax(otherParams.getMax());
                    minAndMaxDTO.setMin(otherParams.getMin());
                    otherParamsList.add(JsonUtil.readTree(JsonUtil.toJsonString(minAndMaxDTO)));
                }
//                MinAndMaxDTO minAndMaxDTO = new MinAndMaxDTO();
//                minAndMaxDTO.setMax(otherParams.getMax());
//                minAndMaxDTO.setMin(otherParams.getMin());
//                ruleInstance.setTargetResult(JsonUtil.toJsonString(minAndMaxDTO));
                ruleInstance.setTargetResult(JsonUtil.toJsonString(otherParamsList));
                ruleInstance.setTargetSql("");
                break;
        }
        ruleInstance.setSourceSql(sourceSql);
        ruleInstance.setBatchno(0);
        if ("prod".equals(env)) {
            ruleInstanceMapper.insert(ruleInstance);
        }
        log.info("env is: {}, rule instance is: {}", env, ruleInstance);
        return ruleInstance;
    }
    
    
    private String getSql(RuleDefinition ruleDefinition, Date date) {
        String sql = "";
        DbDatasourceParam dbDatasourceParam = JsonUtil.readValue(
            ruleDefinition.getDatasourceParams(), DbDatasourceParam.class);
        sql = dbDatasourceParam.getSql();
        RuleDsOtherParams ruleDsOtherParams = JsonUtil.readValue(
            JsonUtil.toJsonString(dbDatasourceParam.getOtherParams()), RuleDsOtherParams.class);
        
        if (ruleDsOtherParams.getSystemVariableParam().size() > 0) {
            for (JsonNode jsonNode : ruleDsOtherParams.getSystemVariableParam()) {
                // 系统变量
                SystemVariableParam systemVariableParam = JsonUtil.readValue(
                    JsonUtil.toJsonString(jsonNode), SystemVariableParam.class);
                if (null != systemVariableParam && StringUtils.isNotEmpty(
                    systemVariableParam.getParams())) {
                    VariableEnum variable = systemVariableParam.getVariable();
                    String value = "";
                    StringBuilder t = new StringBuilder();
                    switch (variable) {
                        case SYSTEM_PRESET:
                            if (StringUtils.isNotEmpty(systemVariableParam.getTimeVariable())) {
                                value = getTimes(systemVariableParam, date);
                            }
                            break;
                        case DEFINE_VARIABLE:
                            Integer variableId = systemVariableParam.getVariableId();
                            log.info("variableId is : {}", variableId);
                            SystemVariable systemVariable = systemVariableMapper.selectById(
                                variableId);
                            log.info("systemVariable is : {}", systemVariable);
                            GetDatasourceResultDTO dto = new GetDatasourceResultDTO();
                            dto.setId(systemVariable.getDatasourceId());
                            dto.setSql(systemVariable.getScriptContent());
                            value = getVariableValue(dto);
                            break;
                    }
                    log.info("value is : {}", value);
                    List<Object> values = null;
                    try {
                        values = extractValuesFromJson(value);
                        log.info("values is : {}", values);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    t.append(values.toString().replaceAll("\\[|\\]", "").replaceAll(" ", ""));

                    sql = sql.replace("${" + systemVariableParam.getParams() + "}", t.toString());
                }
            }
        }
        return sql;
    }
    
    
    private String getSourceSql(RuleDefinition ruleDefinition, Date date) throws Exception {
        String sql = "";
        DbToDbDatasourceParam dbToDbDatasourceParam = JsonUtil.readValue(
            ruleDefinition.getDatasourceParams(), DbToDbDatasourceParam.class);
        sql = dbToDbDatasourceParam.getSourceSql();
        RuleDsOtherParams ruleDsOtherParams = JsonUtil.readValue(
            JsonUtil.toJsonString(dbToDbDatasourceParam.getSourceOtherParams()),
            RuleDsOtherParams.class);
        
        if (ruleDsOtherParams.getSystemVariableParam().size() > 0) {
            for (JsonNode jsonNode : ruleDsOtherParams.getSystemVariableParam()) {
                // 系统变量
                if (StringUtils.isEmpty(jsonNode.get("params").asText())) {
                    continue;
                }
                SystemVariableParam systemVariableParam = JsonUtil.readValue(
                    JsonUtil.toJsonString(jsonNode), SystemVariableParam.class);
                if (null != systemVariableParam && StringUtils.isNotEmpty(
                    systemVariableParam.getParams())) {
                    VariableEnum variable = systemVariableParam.getVariable();
                    String value = "";
                    StringBuilder t = new StringBuilder();
                    switch (variable) {
                        case SYSTEM_PRESET:
//                        case DEFINE_VARIABLE:
                            if (StringUtils.isNotEmpty(systemVariableParam.getTimeVariable())) {
                                value = getTimes(systemVariableParam, date);
                            }
                            break;
                        
                        case DEFINE_VARIABLE:
                            Integer variableId = systemVariableParam.getVariableId();
                            SystemVariable systemVariable = systemVariableMapper.selectById(
                                variableId);
                            GetDatasourceResultDTO dto = new GetDatasourceResultDTO();
                            dto.setId(systemVariable.getDatasourceId());
                            dto.setSql(systemVariable.getScriptContent());
                            value = getVariableValue(dto);
                            break;
                    }
                    log.info("value is : {}", value);
                    List<Object> values = null;
                    try {
                        values = extractValuesFromJson(value);
                        log.info("values is : {}", values);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    t.append(values.toString().replaceAll("\\[|\\]", "").replaceAll(" ", ""));
                    
                    sql = sql.replace("${" + systemVariableParam.getParams() + "}", t.toString());
                }
            }
        }
        return sql;
    }
    
    private String getTargetSql(RuleDefinition ruleDefinition, Date date) {
        String sql = "";
        DbToDbDatasourceParam dbToDbDatasourceParam = JsonUtil.readValue(
            ruleDefinition.getDatasourceParams(), DbToDbDatasourceParam.class);
        if (dbToDbDatasourceParam != null) {
            sql = dbToDbDatasourceParam.getTargetSql();
        }
        RuleDsOtherParams ruleDsOtherParams = null;
        if (dbToDbDatasourceParam != null) {
            ruleDsOtherParams = JsonUtil.readValue(
                JsonUtil.toJsonString(dbToDbDatasourceParam.getTargetOtherParams()),
                RuleDsOtherParams.class);
        }
        if (ruleDsOtherParams != null && ruleDsOtherParams.getSystemVariableParam().size() > 0) {
            for (JsonNode jsonNode : ruleDsOtherParams.getSystemVariableParam()) {
                // 系统变量
                if (StringUtils.isEmpty(jsonNode.get("params").asText())) {
                    continue;
                }
                SystemVariableParam systemVariableParam = JsonUtil.readValue(
                    JsonUtil.toJsonString(jsonNode), SystemVariableParam.class);
                if (null != systemVariableParam && StringUtils.isNotEmpty(
                    systemVariableParam.getParams())) {
                    VariableEnum variable = systemVariableParam.getVariable();
                    String value = "";
                    StringBuilder t = new StringBuilder();
                    switch (variable) {
                        case SYSTEM_PRESET:
                            if (StringUtils.isNotEmpty(systemVariableParam.getTimeVariable())) {
                                value = getTimes(systemVariableParam, date);
                            }
                            break;
                        case DEFINE_VARIABLE:
                            Integer variableId = systemVariableParam.getVariableId();
                            SystemVariable systemVariable = systemVariableMapper.selectById(
                                variableId);
                            GetDatasourceResultDTO dto = new GetDatasourceResultDTO();
                            dto.setId(systemVariable.getDatasourceId());
                            dto.setSql(systemVariable.getScriptContent());
                            value = getVariableValue(dto);
                            break;
                    }
                    log.info("value is : {}", value);
                    List<Object> values = null;
                    try {
                        values = extractValuesFromJson(value);
                        log.info("values is : {}", values);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    t.append(values.toString().replaceAll("\\[|\\]", "").replaceAll(" ", ""));
                    
                    sql = sql.replace("${" + systemVariableParam.getParams() + "}", t.toString());
                }
            }
        }
        
        return sql;
    }
    
    
    private String getTimes(SystemVariableParam ruleDsOtherParams, Date date) {
        String time = "";
        String format = ruleDsOtherParams.getOutputFormat();
        log.info("时间是：{}", ruleDsOtherParams.getTimeVariable());
        time = DateUtil.formatDate(date, format);
        TimeVariableEnum timeVariableEnum = TimeVariableEnum.fromDesc(
            ruleDsOtherParams.getTimeVariable());
        switch (timeVariableEnum) {
            case LAST_DAY:
                date = DateUtil.getCurrentDay(-1);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_TWO_DAY:
                date = DateUtil.getCurrentDay(-2);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_THREE_DAY:
                date = DateUtil.getCurrentDay(-3);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_MONTH:
                date = DateUtil.getCurrentMonth(-1);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_TWO_MONTH:
                date = DateUtil.getCurrentMonth(-2);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_THREE_MONTH:
                date = DateUtil.getCurrentMonth(-3);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_YEAR:
                date = DateUtil.getCurrentYear(-1);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_TWO_YEAR:
                date = DateUtil.getCurrentYear(-2);
                time = DateUtil.formatDate(date, format);
                break;
            case LAST_THREE_YEAR:
                date = DateUtil.getCurrentYear(-3);
                time = DateUtil.formatDate(date, format);
                break;
            default:
                break;
        }
        return time;
    }
    
    /**
     * 执行实例
     *
     * @param ruleInstance
     */
    private Boolean runRuleInstance(RuleInstance ruleInstance, String env) {
        Date startTime = DateUtil.getCurrentDate();
        ruleInstance.setStartDate(DateUtil.formatDate(startTime, DateUtil.FORMAT_S));
        try {
            ruleInstance.setServerName(OSUtils.getHost());
            processTask(ruleInstance, env);
            Date endTime = DateUtil.getCurrentDate();
            long timeDiff = endTime.getTime() - startTime.getTime();
            double timeDiffInSeconds = timeDiff / 1000;
            ruleInstance.setEndDate(DateUtil.formatDate(endTime, DateUtil.FORMAT_S));
            ruleInstance.setState(InstanceStateEnum.AUDITING);
            ruleInstance.setRunTimes(timeDiffInSeconds);
            if ("prod".equals(env)) {
                ruleInstanceMapper.updateById(ruleInstance);
            }
            log.info("env is: {}, run rule instance is: {}", env, ruleInstance);
            return true;
        } catch (Exception e) {
            log.error("任务执行失败，错误信息 :{}", e);
            ruleInstance.setState(InstanceStateEnum.EXECUTOR_FAILURE);
            Date endTime = DateUtil.getCurrentDate();
            ruleInstance.setEndDate(DateUtil.formatDate(endTime, DateUtil.FORMAT_S));
            String msg = e.toString();
            if (msg.length() > 1000) {
                msg = msg.substring(0, 1000);
            }
            ruleInstance.setErrorValue(msg);
            if ("prod".equals(env)) {
                ruleInstanceMapper.updateErrorMsg(ruleInstance);
            }
            log.info("Error:env is {}, run rule instance failed, ruleInstance is {}", env,
                ruleInstance);
            return false;
        }
    }
    
    
    /**
     * 获取目录下所有的规则
     *
     * @param ids
     * @return
     */
    private List<RuleDefinition> getRuleDefinitionList(List<Integer> ids) {
        List<RuleDefinition> ruleDefinitionList = new ArrayList<>();
        for (Integer id : ids) {
            ruleDefinitionList = ruleDefinitionMapper.selectRuleListByCategory(id);
            
        }
        return ruleDefinitionList;
    }
    
    
    private List<Category> getCategory(int i) {
        List<Category> categorieList = categoryMapper.selectByPid(
            i);
        for (Category category : categorieList) {
            List<Category> list = categoryMapper.selectByPid(
                category.getId());
            category.setCategories(list);
            if (categoryMapper.selectByPid(i).size() > 0) {
                getCategory(category.getId());
            }
        }
        return categorieList;
    }
    
    public void processTask(RuleInstance ruleInstance, String env) throws Exception {
        RuleDefinition ruleDefinition = ruleDefinitionMapper.selectRuleDefinitionById(
            ruleInstance.getRuleDefinitionId());
        AuditTypeEnum type = ruleDefinition.getType();
        BaseUtil<Connection> util = new SqlUtil();
        ConnectionPool<Connection> instance = null;
        String sourceSql = ruleInstance.getSourceSql();
        Datasource datasource = null;
        switch (type) {
            case DBTODB:
                // 对于DBTODB类型，数据获取和处理逻辑已移至runAudit方法，此处留空以避免OOM
                break;
            case DB:
                DbDatasourceParam dbDatasourceParam = JsonUtil.readValue(
                    ruleDefinition.getDatasourceParams(), DbDatasourceParam.class);
                
                String dbKey = CacheUtil.getDataSourceCacheKey(dbDatasourceParam.getDs());
                String dbDatasourceCacheValue = LocalCacheUtil.getValue(dbKey);
                
                if (StringUtils.isNotEmpty(dbDatasourceCacheValue)) {
                    DatasourceCache datasourceCache = JsonUtil.readValue(dbDatasourceCacheValue,
                        DatasourceCache.class);
                    datasource = ConvertUtils.convert(datasourceCache, new Datasource());
                } else {
                    datasource = datasourceMapper.selectDatasourceById(dbDatasourceParam.getDs());
                    DatasourceCache result = ConvertUtils.convert(datasource,
                        new DatasourceCache());
                    LocalCacheUtil.setValue(dbKey, JsonUtil.toJsonString(result));
                }
                
                String dbSourceResult = "";
                //执行SQL
                if (datasource.getDsType() == 11) {
                    // IMPALA数据源类型
                    
                    String connectionParams = datasource.getConnectionParams();
                    DataSourceProcessor dataSourceProcessor = DataSourceUtil.getDataSourceProcessor(
                        DbType.IMPALA);
                    ConnectionParam connectionProdParams = dataSourceProcessor.createConnectionParams(
                        connectionParams);
                    log.info("impala datasource connection params: {}", connectionProdParams);
                    //获取连接
                    Connection impalaConnection = getImpalaConnection(connectionProdParams);
                    dbSourceResult = util.getResultObject(impalaConnection, sourceSql, datasource);
                } else if (datasource.getDsType() == 40) {
                    // Teradata数据源类型
                    
                    String connectionParams = datasource.getConnectionParams();
                    DataSourceProcessor dataSourceProcessor = DataSourceUtil.getDataSourceProcessor(
                        DbType.TERADATA);
                    ConnectionParam connectionProdParams = dataSourceProcessor.createConnectionParams(
                        connectionParams);
                    log.info("teradata datasource connection params: {}", connectionProdParams);
                    //获取连接
                    Connection teradataConnection = dataSourceProcessor.getConnection(
                        connectionProdParams);
                    dbSourceResult = util.getResultObject(teradataConnection, sourceSql,
                        datasource);
                } else {
                    switch (datasource.getType()) {
                        case SQL:
                            instance = JdbcConnectionPool.getInstance(sqlProperties);
                            dbSourceResult = util.getResultObject(instance, sourceSql, datasource);
//                        SqlDatasourceParam sqlDatasourceParam = datasourceParamUtil.parseConnectionParams(connectionParams, datasource.getDsType());
                            break;
                        case HQL:
                            String connectionParams = datasource.getConnectionParams();
                            //修改版
                            // connectionParams: {"user":"idp","password":"******", ...}
                            HqlDatasourceParam hqlDatasourceParam = datasourceConnectionParamUtil.parseHqlConnectionParams(
                                connectionParams, datasource.getDsType());
                            if (VerificationEnum.KERBEROS.equals(
                                hqlDatasourceParam.getVerification())) {
                                KerberosParam kerberosParam = hqlDatasourceParam.getKerberosParam();
                                if (StringUtils.isNotEmpty(kerberosParam.getKeytabPath())) {
                                    DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                        datasource.getId());
                                    String fileName =
                                        "/idp_" + datasourceFile.getDatasourceId() + ".keytab";
                                    ConfigFile keytabFile = new ConfigFile();
                                    keytabFile.setFileName(fileName);
                                    keytabFile.setFilePath(publicKeysStorePath + fileName);
                                    keytabFile.setFileContent(datasourceFile.getKeytab());
                                    byteToFile(
                                        EncryptUtil.base64Decode(keytabFile.getFileContent()),
                                        keytabFile.getFilePath());
                                    kerberosParam.setKeytabPath(keytabFile.getFilePath());
                                }
                                if (StringUtils.isNotEmpty(kerberosParam.getKrb5Path())) {
                                    DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                        datasource.getId());
                                    String fileName =
                                        "/krb5_" + datasourceFile.getDatasourceId() + ".conf";
                                    ConfigFile krb5File = new ConfigFile();
                                    krb5File.setFileName(fileName);
                                    krb5File.setFilePath(publicKeysStorePath + fileName);
                                    krb5File.setFileContent(datasourceFile.getKrb5());
                                    byteToFile(EncryptUtil.base64Decode(krb5File.getFileContent()),
                                        krb5File.getFilePath());
                                    kerberosParam.setKrb5Path(krb5File.getFilePath());
                                }
                                if (StringUtils.isNotEmpty(kerberosParam.getJaasPath())) {
                                    DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                        datasource.getId());
                                    String fileName =
                                        "/jaas_" + datasourceFile.getDatasourceId() + ".conf";
                                    ConfigFile jaasFile = new ConfigFile();
                                    jaasFile.setFileName(fileName);
                                    jaasFile.setFilePath(publicKeysStorePath + fileName);
                                    jaasFile.setFileContent(datasourceFile.getJaas());
                                    byteToFile(EncryptUtil.base64Decode(jaasFile.getFileContent()),
                                        jaasFile.getFilePath());
                                    kerberosParam.setJaasPath(jaasFile.getFilePath());
                                }
                                hqlDatasourceParam.setKerberosParam(kerberosParam);
                                datasource.setConnectionParams(
                                    JsonUtil.toJsonString(hqlDatasourceParam));
                            }
                            
                            instance = HqlConnectionPool.getInstance();
                            dbSourceResult = util.getResultObject(instance, sourceSql, datasource);
                            break;
                    }
                }
                
                ruleInstance.setSourceResult(dbSourceResult);
                if ("prod".equals(env)) {
                    ruleInstanceMapper.updateById(ruleInstance);
                }
                log.info("environment is {}, processed rule instance is : {]", env, ruleInstance);
                break;
        }
    }
    
    public void byteToFile(byte[] bytes, String file) {
        
        Path localPath = Paths.get(file).getParent();
        if (Files.notExists(localPath)) {
            try {
                Files.createDirectories(localPath);
            } catch (IOException e) {
                log.error("Error : can not create dir : {}", localPath);
            }
        }
        
        if (Files.exists(Paths.get(file))) {
            try {
                Files.delete(Paths.get(file));
            } catch (IOException e) {
                log.error("Error : can not delete dir : {}", file);
            }
        }
        
        try (OutputStream os = new FileOutputStream(file)) {
            os.write(bytes);
            os.flush();
        } catch (Exception e) {
            log.error("Error : can not write file {}", file);
        }
    }
    
    private String getResult(String sql, Datasource datasource, String jdbcAddress)
        throws Exception {
        ConnectionPool<Connection> instance = null;
        BaseUtil<Connection> util = new SqlUtil();
        String result = "";
        //执行SQL
        if (datasource.getDsType() == 11) {
            //IMPALA数据源类型
            
            String connectionParams = datasource.getConnectionParams();
            DataSourceProcessor dataSourceProcessor = DataSourceUtil.getDataSourceProcessor(
                DbType.IMPALA);
            ConnectionParam connectionProdParams = dataSourceProcessor.createConnectionParams(
                connectionParams);
            log.info("impala datasource connection params:{}", connectionProdParams);
            //获取连接
            Connection impalaConnection = getImpalaConnection(connectionProdParams);
            result = util.getResultObject(impalaConnection, sql, datasource);
        } else if (datasource.getDsType() == 40) {
            //Teradata数据源类型
            String connectionParams = datasource.getConnectionParams();
            DataSourceProcessor dataSourceProcessor = DataSourceUtil.getDataSourceProcessor(
                DbType.TERADATA);
            ConnectionParam connectionProdParams = dataSourceProcessor.createConnectionParams(
                connectionParams);
            log.info("teradata datasource connection params:{}", connectionProdParams);
            //获取连接
            Connection teradataConnection = dataSourceProcessor.getConnection(connectionProdParams);
            result = util.getResultObject(teradataConnection, sql, datasource);
        } else {
            switch (datasource.getType()) {
                case SQL:
                    SqlDatasourceParam sqlDatasourceParam = datasourceConnectionParamUtil.parseConnectionParams(
                        datasource.getConnectionParams(), datasource.getDsType());
                    if (StringUtils.isNotEmpty(jdbcAddress)) {
                        sqlDatasourceParam.setSqlJdbcUrl(jdbcAddress);
                    }
                    instance = JdbcConnectionPool.getInstance(sqlProperties);
                    result = util.getResultObject(instance, sql, datasource);
                    break;
                case HQL:
                    //修改版
                    HqlDatasourceParam hqlDatasourceParam = datasourceConnectionParamUtil.parseHqlConnectionParams(
                        datasource.getConnectionParams(), datasource.getDsType());
                    if (VerificationEnum.KERBEROS.equals(hqlDatasourceParam.getVerification())) {
                        KerberosParam kerberosParam = hqlDatasourceParam.getKerberosParam();
                        if (StringUtils.isNotEmpty(kerberosParam.getKeytabPath())) {
                            DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                datasource.getId());
                            String fileName =
                                "/idp_" + datasourceFile.getDatasourceId() + ".keytab";
                            ConfigFile keytabFile = new ConfigFile();
                            keytabFile.setFileName(fileName);
                            keytabFile.setFilePath(publicKeysStorePath + fileName);
                            keytabFile.setFileContent(datasourceFile.getKeytab());
                            byteToFile(EncryptUtil.base64Decode(keytabFile.getFileContent()),
                                keytabFile.getFilePath());
                            kerberosParam.setKeytabPath(keytabFile.getFilePath());
                        }
                        if (StringUtils.isNotEmpty(kerberosParam.getKrb5Path())) {
                            DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                datasource.getId());
                            String fileName = "/krb5_" + datasourceFile.getDatasourceId() + ".conf";
                            ConfigFile krb5File = new ConfigFile();
                            krb5File.setFileName(fileName);
                            krb5File.setFilePath(publicKeysStorePath + fileName);
                            krb5File.setFileContent(datasourceFile.getKrb5());
                            byteToFile(EncryptUtil.base64Decode(krb5File.getFileContent()),
                                krb5File.getFilePath());
                            kerberosParam.setKrb5Path(krb5File.getFilePath());
                        }
                        if (StringUtils.isNotEmpty(kerberosParam.getJaasPath())) {
                            DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                datasource.getId());
                            String fileName = "/jaas_" + datasourceFile.getDatasourceId() + ".conf";
                            ConfigFile jaasFile = new ConfigFile();
                            jaasFile.setFileName(fileName);
                            jaasFile.setFilePath(publicKeysStorePath + fileName);
                            jaasFile.setFileContent(datasourceFile.getJaas());
                            byteToFile(EncryptUtil.base64Decode(jaasFile.getFileContent()),
                                jaasFile.getFilePath());
                            kerberosParam.setJaasPath(jaasFile.getFilePath());
                        }
                        
                        hqlDatasourceParam.setKerberosParam(kerberosParam);
                        datasource.setConnectionParams(JsonUtil.toJsonString(hqlDatasourceParam));
                    }
                    instance = HqlConnectionPool.getInstance();
                    result = util.getResultObject(instance, sql, datasource);
                    break;
            }
        }
        
        return result;
    }

//    private String getVariableValue(GetDatasourceResultDTO dto) {
//        String response;
//        String url = serviceConfig.getUrl();
//        String request;
//        ApiProtocolEnum protocol = serviceConfig.getProtocol();
//        log.info("url is:{}, protocol is :{}", url, protocol.name());
//
//        CloseableHttpClient httpClient = HttpClientUtil.getHttpClient();
//        if (ApiProtocolEnum.HTTPS.equals(protocol)) {
//            httpClient = HttpClientUtil.getHttpsClient();
//        }
//        request = JsonUtil.toJsonString(dto);
//        StringBuilder urlBuilder = new StringBuilder(url);
//        urlBuilder.append("/api/datasource/result");
//        response = HttpClientUtil.executePost(httpClient, urlBuilder.toString(), request);
//        List<Map<String, String>> list = JsonUtil.readMapList(response);
//        response = list.get(0).values().iterator().next();
//        return response;
//    }
    
    
    private String getVariableValue(GetDatasourceResultDTO dto) {
        return getDatasourceResult(dto);
    }
    
    private String getDatasourceResult(GetDatasourceResultDTO dto) {
        String result = "";
        Datasource datasource = datasourceMapper.selectDatasourceById(dto.getId());
        String sql = dto.getSql();
        ConnectionPool<Connection> instance = null;
        
        //执行SQL
        try {
            switch (datasource.getType()) {
                case SQL:
                    BaseUtil<Connection> sqlUtil = new SqlUtil();
                    instance = JdbcConnectionPool.getInstance(sqlProperties);
                    result = sqlUtil.getResultObject(instance, sql, datasource);
                    break;
                case HQL:
                    BaseUtil<Connection> hqlUtil = new HqlDataapiUtil();
                    HqlDatasourceParam hqlDatasourceParam = JsonUtil.readValue(
                        datasource.getConnectionParams(), HqlDatasourceParam.class);
                    if (VerificationEnum.KERBEROS.equals(hqlDatasourceParam.getVerification())) {
                        KerberosParam kerberosParam = hqlDatasourceParam.getKerberosParam();
                        if (StringUtils.isNotEmpty(kerberosParam.getKeytabPath())) {
                            DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                datasource.getId());
                            String fileName =
                                "/idp_" + datasourceFile.getDatasourceId() + ".keytab";
                            ConfigFile keytabFile = new ConfigFile();
                            keytabFile.setFileName(fileName);
                            keytabFile.setFilePath(publicKeysStorePath + fileName);
                            keytabFile.setFileContent(datasourceFile.getKeytab());
                            byteToFile(EncryptUtil.base64Decode(keytabFile.getFileContent()),
                                keytabFile.getFilePath());
                            kerberosParam.setKeytabPath(keytabFile.getFilePath());
                        }
                        if (StringUtils.isNotEmpty(kerberosParam.getKrb5Path())) {
                            DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                datasource.getId());
                            String fileName = "/krb5_" + datasourceFile.getDatasourceId() + ".conf";
                            ConfigFile krb5File = new ConfigFile();
                            krb5File.setFileName(fileName);
                            krb5File.setFilePath(publicKeysStorePath + fileName);
                            krb5File.setFileContent(datasourceFile.getKrb5());
                            byteToFile(EncryptUtil.base64Decode(krb5File.getFileContent()),
                                krb5File.getFilePath());
                            kerberosParam.setKrb5Path(krb5File.getFilePath());
                        }
                        if (StringUtils.isNotEmpty(kerberosParam.getJaasPath())) {
                            DatasourceFile datasourceFile = datasourceFileMapper.selectItemByDatasourceId(
                                datasource.getId());
                            String fileName = "/jaas_" + datasourceFile.getDatasourceId() + ".conf";
                            ConfigFile jaasFile = new ConfigFile();
                            jaasFile.setFileName(fileName);
                            jaasFile.setFilePath(publicKeysStorePath + fileName);
                            jaasFile.setFileContent(datasourceFile.getJaas());
                            byteToFile(EncryptUtil.base64Decode(jaasFile.getFileContent()),
                                jaasFile.getFilePath());
                            kerberosParam.setJaasPath(jaasFile.getFilePath());
                        }
                        
                        hqlDatasourceParam.setKerberosParam(kerberosParam);
                        datasource.setConnectionParams(JsonUtil.toJsonString(hqlDatasourceParam));
                    }
                    instance = HqlConnectionPool.getInstance();
                    result = hqlUtil.getResultObject2(instance, sql, datasource);
                    break;
            }
        } catch (Exception e) {
            log.error("Error : Query sql error : {}", e);
        }
        return result;
    }
    
    /**
     * 获取IMPALA Connection
     *
     * @param connectionProdParams
     * @return
     */
    private Connection getImpalaConnection(ConnectionParam connectionProdParams)
        throws ClassNotFoundException, IOException, InterruptedException, SQLException {
        ImpalaDataSourceProcessor impalaDataSourceProcessor = new ImpalaDataSourceProcessor();
        Connection connection = null;
        ImpalaConnectionParam impalaConnectionParam = (ImpalaConnectionParam) connectionProdParams;
        String driverClassName = impalaConnectionParam.getDriverClassName() == null
            ? impalaDataSourceProcessor.getDataSourceDriver()
            : impalaConnectionParam.getDriverClassName();
        log.info("driver class name: {}", driverClassName);
        Class.forName(driverClassName);
        String url = impalaDataSourceProcessor.getJdbcUrl(connectionProdParams);
        log.info("jdbc url: {}", url);
        Configuration conf;
        String krb5Path = PropertyUtils.getString(
            org.apache.dolphinscheduler.common.constant.Constants.JAVA_SECURITY_KRB5_CONF_PATH);
        log.info("krb5Path: {}", krb5Path);
        System.setProperty("java.security.krb5.conf", krb5Path);
//        System.setProperty("sun.security.krb5.debug", "true");
        switch (impalaConnectionParam.getAuthType()) {
            case KERBEROS:
                String realm = PropertyUtils.getString(
                    org.apache.dolphinscheduler.common.constant.Constants.KDC_REALM);
                String keytabDir = PropertyUtils.getString(
                    org.apache.dolphinscheduler.common.constant.Constants.LOGIN_USER_KEY_TAB_DIR);
                keytabDir = String.format("%s/%s.keytab", keytabDir,
                    impalaConnectionParam.getUser());
                log.info("keytabDir: {}", keytabDir);
                String principal = String.format("%s@%s", impalaConnectionParam.getUser(), realm);
                log.info("principal: {}", principal);
                conf = new Configuration();
                conf.set("hadoop.security.authentication", "Kerberos");
                UserGroupInformation.setConfiguration(conf);
                UserGroupInformation userGroupInformation = UserGroupInformation.loginUserFromKeytabAndReturnUGI(
                    principal, keytabDir);
                connection = userGroupInformation.doAs(
                    (PrivilegedExceptionAction<Connection>) () -> DriverManager.getConnection(url));
                break;
            default:
                connection = DriverManager.getConnection(url);
                break;
        }
        return connection;
    }
    
    /**
     * 流式对比并记录所有差异到CSV文件
     * 注意：如果结果只有一行数据，不创建CSV文件，直接记录到数据库
     * @return 包含差异信息的 DTO，若无差异始终返回非null的DTO
     */
    private StreamDiffResult streamDiffAndPreview(Connection conn1, String sql1, String dbType1,
        Connection conn2, String sql2, String dbType2, String taskName, String ruleName) throws SQLException {
        StreamDiffResult result = new StreamDiffResult();
        final int previewLimit = 10;
        final int csvThreshold = 1; // 超过1行才创建CSV文件
        DiffCsvWriter csvWriter = null;

        try (
            Statement stmt1 = conn1.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            Statement stmt2 = conn2.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY)
        ) {
            // fetchSize 适配
            if ("mysql".equalsIgnoreCase(dbType1) || "doris".equalsIgnoreCase(dbType1)) {
                stmt1.setFetchSize(Integer.MIN_VALUE);
            } else {
                stmt1.setFetchSize(1000);
            }
            if ("mysql".equalsIgnoreCase(dbType2) || "doris".equalsIgnoreCase(dbType2)) {
                stmt2.setFetchSize(Integer.MIN_VALUE);
            } else {
                stmt2.setFetchSize(1000);
            }

            try (
                ResultSet rs1 = stmt1.executeQuery(sql1);
                ResultSet rs2 = stmt2.executeQuery(sql2)
            ) {
                ResultSetMetaData meta1 = rs1.getMetaData();
                int colCount = meta1.getColumnCount();

                List<Map<String, String>> previewSourceRows = new ArrayList<>();
                List<Map<String, String>> previewTargetRows = new ArrayList<>();

                // 用于记录第一个差异行（用于数据库记录）
                Map<String, String> firstDiffSourceRow = null;
                Map<String, String> firstDiffTargetRow = null;

                long rowNum = 0;
                boolean shouldCreateCsv = false; // 标记是否需要创建CSV文件

                while (rs1.next() && rs2.next()) {
                    rowNum++;
                    Map<String, String> sourceRowMap = new java.util.HashMap<>();
                    Map<String, String> targetRowMap = new java.util.HashMap<>();
                    boolean isRowDifferent = false;
                    String diffColumn = null;

                    // 收集所有差异字段
                    List<String> diffColumns = new ArrayList<>();

                    for (int i = 1; i <= colCount; i++) {
                        String colName = meta1.getColumnName(i);
                        String v1 = rs1.getString(i);
                        String v2 = rs2.getString(i);
                        sourceRowMap.put(colName, v1);
                        targetRowMap.put(colName, v2);

                        if (!Objects.equals(v1, v2)) {
                            // 发现字段不一致
                            diffColumns.add(colName);

                            if (!isRowDifferent) {
                                // 首次发现不一致
                                isRowDifferent = true;
                                diffColumn = colName; // 保留第一个差异字段用于数据库记录
                                result.hasDiff = true;

                                // 记录第一个差异行用于数据库存储
                                if (firstDiffSourceRow == null) {
                                    firstDiffSourceRow = new java.util.HashMap<>(sourceRowMap);
                                    firstDiffTargetRow = new java.util.HashMap<>(targetRowMap);
                                    result.errorValue = String.format("行 %d, 字段 %s 不一致: 源端='%s', 目标端='%s'",
                                        rowNum, colName, v1, v2);
                                }
                            }
                        }
                    }

                    // 收集预览数据
                    if (rowNum <= previewLimit) {
                        previewSourceRows.add(sourceRowMap);
                        previewTargetRows.add(targetRowMap);
                    }

                    // 判断是否需要创建CSV文件：超过1行且有差异
                    if (isRowDifferent && rowNum > csvThreshold) {
                        shouldCreateCsv = true;
                    }

                    // 如果发现差异且需要创建CSV文件，则写入CSV
                    if (isRowDifferent && shouldCreateCsv) {
                        // 延迟创建CSV写入器（只有在需要时才创建）
                        if (csvWriter == null) {
                            try {
                                csvWriter = new DiffCsvWriter(errorWriterFilePath, taskName, ruleName);
                                result.diffFilePath = csvWriter.getFilePath();
                                log.info("[DQ流式对比] 数据超过{}行且存在差异，创建CSV文件记录详情: {}",
                                    csvThreshold, result.diffFilePath);
                            } catch (Exception e) {
                                log.error("[DQ流式对比] 创建差异文件失败: {}", e.getMessage(), e);
                                // 即使文件创建失败，也继续对比流程
                            }
                        }

                        // 写入差异记录到CSV
                        if (csvWriter != null) {
                            try {
                                // 将所有差异字段传递给CSV写入器，使用分号分隔
                                String allDiffColumns = String.join(";", diffColumns);
                                csvWriter.writeDiffRow((int)rowNum, "字段值不一致", allDiffColumns,
                                    sourceRowMap, targetRowMap);
                            } catch (Exception e) {
                                log.error("[DQ流式对比] 写入差异记录失败: {}", e.getMessage(), e);
                            }
                        }
                    }
                }

                result.totalProcessedRows = rowNum;

                // 检查行数不一致
                long sourceExtraRows = 0;
                long targetExtraRows = 0;

                while (rs1.next()) {
                    sourceExtraRows++;
                    rowNum++;
                }

                while (rs2.next()) {
                    targetExtraRows++;
                    rowNum++;
                }

                if (sourceExtraRows > 0 || targetExtraRows > 0) {
                    result.hasDiff = true;
                    result.errorValue = String.format("源端和目标端总行数不一致，源端多 %d 行，目标端多 %d 行",
                        sourceExtraRows, targetExtraRows);

                    // 行数不一致时，如果总行数超过阈值，也创建CSV文件记录
                    if (result.totalProcessedRows > csvThreshold) {
                        shouldCreateCsv = true;

                        if (csvWriter == null && shouldCreateCsv) {
                            try {
                                csvWriter = new DiffCsvWriter(errorWriterFilePath, taskName, ruleName);
                                result.diffFilePath = csvWriter.getFilePath();
                                log.info("[DQ流式对比] 发现行数不一致且数据超过{}行，创建CSV文件记录: {}",
                                    csvThreshold, result.diffFilePath);
                            } catch (Exception e) {
                                log.error("[DQ流式对比] 创建差异文件失败: {}", e.getMessage(), e);
                            }
                        }

                        if (csvWriter != null) {
                            try {
                                csvWriter.writeRowCountDiff(result.totalProcessedRows - targetExtraRows + sourceExtraRows,
                                    result.totalProcessedRows - sourceExtraRows + targetExtraRows);
                            } catch (Exception e) {
                                log.error("[DQ流式对比] 写入行数差异失败: {}", e.getMessage(), e);
                            }
                        }
                    }
                }

                // 设置返回结果
                if (result.hasDiff) {
                    if (firstDiffSourceRow != null && firstDiffTargetRow != null) {
                        result.diffSourceRowJson = JsonUtil.toJsonString(java.util.Collections.singletonList(firstDiffSourceRow));
                        result.diffTargetRowJson = JsonUtil.toJsonString(java.util.Collections.singletonList(firstDiffTargetRow));
                    }
                    if (csvWriter != null) {
                        result.totalDiffCount = csvWriter.getDiffCount();
                    } else {
                        // 没有创建CSV文件时，差异数量就是1（单行数据差异）
                        result.totalDiffCount = 1;
                    }
                } else {
                    // 完整遍历且无差异
                    result.previewSourceRowsJson = JsonUtil.toJsonString(previewSourceRows);
                    result.previewTargetRowsJson = JsonUtil.toJsonString(previewTargetRows);
                }

                return result;
            }
        } finally {
            // 确保CSV文件正确关闭
            if (csvWriter != null) {
                try {
                    csvWriter.close();
                } catch (Exception e) {
                    log.error("[DQ流式对比] 关闭差异文件失败: {}", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 为JDBC url安全拼接useSSL=false&useCursorFetch=true，仅限MySQL和Doris，Teradata不拼接
     */
    private String appendStreamParams(String url) {
        if (url == null)
            return null;
        String lowerUrl = url.toLowerCase();
        // 只对MySQL和Doris拼接参数
        if (lowerUrl.startsWith("jdbc:mysql://") || lowerUrl.startsWith("jdbc:doris://")) {
            String paramStr = "useSSL=false&useCursorFetch=true";
            // 已有参数则追加，否则加?
            boolean hasSSL = lowerUrl.contains("usessl");
            boolean hasCursorFetch = lowerUrl.contains("usecursorfetch");
            if (url.contains("?")) {
                if (!hasSSL)
                    url += (url.endsWith("&") ? "" : "&") + "useSSL=false";
                if (!hasCursorFetch)
                    url += (url.endsWith("&") ? "" : "&") + "useCursorFetch=true";
            } else {
                url += "?";
                if (!hasSSL && !hasCursorFetch) {
                    url += paramStr;
                } else if (!hasSSL) {
                    url += "useSSL=false";
                } else if (!hasCursorFetch) {
                    url += "useCursorFetch=true";
                }
            }
            return url;
        }
        // Teradata和其他类型不拼接参数
        return url;
    }

    /**
     * 从规则实例中获取任务名称
     */
    private String getTaskNameFromInstance(RuleInstance ruleInstance) {
        try {
            //获取task_instance_id 根据此id 查询task_instance表获取task_name
            if(ruleInstance.getTaskInstanceId() != null) {
                String taskName = taskInstanceMapper.getTaskNameByTaskInstanceId(
                    ruleInstance.getTaskInstanceId());
                if (taskName != null) {
                    return taskName;
                }
            }
            // 尝试从实例名称中提取任务名称
            String instanceName = ruleInstance.getName();
            if (instanceName != null && instanceName.contains("-")) {
                // 实例名称格式通常是: 规则名称-批次号-时间戳
                String[] parts = instanceName.split("-");
                if (parts.length >= 1) {
                    return parts[0];
                }
            }

            // 如果有taskInstanceId，可以通过它查询任务名称
            if (ruleInstance.getTaskInstanceId() != null) {
                // 这里可以添加查询任务表的逻辑，暂时使用taskInstanceId作为任务名
                return "Task_" + ruleInstance.getTaskInstanceId();
            }

            // 默认使用规则实例ID
            return "RuleInstance_" + ruleInstance.getId();
        } catch (Exception e) {
            log.warn("[DQ流式对比] 获取任务名称失败，使用默认名称: {}", e.getMessage());
            return "UnknownTask";
        }
    }

    /**
     * 用于流式对比返回结果的内部 DTO
     */
    private static class StreamDiffResult {
        boolean hasDiff = false;
        String errorValue;
        String diffSourceRowJson;
        String diffTargetRowJson;
        String previewSourceRowsJson;
        String previewTargetRowsJson;
        // 新增字段
        int totalDiffCount = 0;           // 总差异行数
        String diffFilePath;              // 差异文件路径
        long totalProcessedRows = 0;      // 总处理行数
    }

}