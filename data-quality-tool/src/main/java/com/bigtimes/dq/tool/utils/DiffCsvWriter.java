package com.bigtimes.dq.tool.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 数据质量差异CSV文件写入工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
public class DiffCsvWriter implements AutoCloseable {
    
    private final String filePath;
    private final PrintWriter writer;
    private boolean headerWritten = false;
    private int diffCount = 0;
    
    /**
     * 构造函数
     * 
     * @param baseDir 基础目录
     * @param taskName 任务名称
     * @param ruleName 规则名称
     * @throws IOException IO异常
     */
    public DiffCsvWriter(String baseDir, String taskName, String ruleName) throws IOException {
        this.filePath = generateFilePath(baseDir, taskName, ruleName);
        createDirectoryIfNotExists(this.filePath);
        this.writer = new PrintWriter(new OutputStreamWriter(
            new FileOutputStream(this.filePath), StandardCharsets.UTF_8));
        log.info("[DQ差异文件] 创建差异记录文件: {}", this.filePath);
    }
    
    /**
     * 生成文件路径
     */
    private String generateFilePath(String baseDir, String taskName, String ruleName) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat timeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateStr = dateFormat.format(new Date());
        String timeStr = timeFormat.format(new Date());
        
        // 清理文件名中的特殊字符
        String cleanTaskName = cleanFileName(taskName);
        String cleanRuleName = cleanFileName(ruleName);
        
        String fileName = String.format("%s_%s_%s.csv", cleanTaskName, cleanRuleName, timeStr);
        return Paths.get(baseDir,  dateStr, cleanTaskName,fileName).toString();
    }
    
    /**
     * 清理文件名中的特殊字符
     */
    private String cleanFileName(String name) {
        if (StringUtils.isEmpty(name)) {
            return "unknown";
        }
        // 替换文件名中不允许的字符
        return name.replaceAll("[\\\\/:*?\"<>|]", "_")
                  .replaceAll("\\s+", "_")
                  .trim();
    }
    
    /**
     * 创建目录
     */
    private void createDirectoryIfNotExists(String filePath) throws IOException {
        Path parentDir = Paths.get(filePath).getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
            log.info("[DQ差异文件] 创建目录: {}", parentDir);
        }
    }
    
    /**
     * 写入CSV头部
     */
    public void writeHeader(Map<String, String> sourceRow, Map<String, String> targetRow) {
        if (headerWritten) {
            return;
        }
        
        // 写入CSV头部
        writer.print("行号,差异类型,不一致字段");
        
        // 添加源端字段列
        for (String columnName : sourceRow.keySet()) {
            writer.print(",源端_" + escapeCSV(columnName));
        }
        
        // 添加目标端字段列
        for (String columnName : targetRow.keySet()) {
            writer.print(",目标端_" + escapeCSV(columnName));
        }
        
        writer.println();
        headerWritten = true;
        log.debug("[DQ差异文件] 写入CSV头部");
    }
    
    /**
     * 写入差异行数据
     */
    public void writeDiffRow(int rowNumber, String diffType, String allDiffColumns,
                           Map<String, String> sourceRow, Map<String, String> targetRow) {
        if (!headerWritten) {
            writeHeader(sourceRow, targetRow);
        }
        
        // 写入基本信息
        writer.print(rowNumber);
        writer.print(",");
        writer.print(escapeCSV(diffType));
        writer.print(",");
        writer.print(escapeCSV(allDiffColumns));
        
        // 写入源端数据
        for (String columnName : sourceRow.keySet()) {
            writer.print(",");
            writer.print(escapeCSV(sourceRow.get(columnName)));
        }
        
        // 写入目标端数据
        for (String columnName : targetRow.keySet()) {
            writer.print(",");
            writer.print(escapeCSV(targetRow.get(columnName)));
        }
        
        writer.println();
        diffCount++;
        
        // 每100行刷新一次
        if (diffCount % 100 == 0) {
            writer.flush();
            log.debug("[DQ差异文件] 已写入 {} 行差异记录", diffCount);
        }
    }

    /**
     * 写入差异行数据（兼容性方法）
     */
    public void writeDiffRow(int rowNumber, String diffType, String diffColumn,
                           Map<String, String> sourceRow, Map<String, String> targetRow) {
        // 调用新方法，diffColumn作为allDiffColumns
        writeDiffRow(rowNumber, diffType, diffColumn, sourceRow, targetRow);
    }

    /**
     * 写入行数不一致的记录
     */
    public void writeRowCountDiff(long sourceCount, long targetCount) {
        if (!headerWritten) {
            // 如果还没写过头部，写一个简化的头部
            writer.println("行号,差异类型,描述");
            headerWritten = true;
        }
        
        writer.printf("N/A,行数不一致,源端行数:%d 目标端行数:%d%n", sourceCount, targetCount);
        diffCount++;
        writer.flush();
    }
    
    /**
     * 转义CSV字段值
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        
        return value;
    }
    
    /**
     * 获取文件路径
     */
    public String getFilePath() {
        return filePath;
    }
    
    /**
     * 获取差异记录数量
     */
    public int getDiffCount() {
        return diffCount;
    }
    
    /**
     * 关闭文件写入器
     */
    @Override
    public void close() {
        if (writer != null) {
            writer.flush();
            writer.close();
            log.info("[DQ差异文件] 关闭差异记录文件: {}, 共记录 {} 行差异", filePath, diffCount);
        }
    }
}
